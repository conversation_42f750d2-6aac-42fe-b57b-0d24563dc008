# Stock Resource API Contract

## 1. GET /stocks/:id

**Request**

- Path parameter:
  - `id`: string

**Response**
```json
{
  "code": "string",
  "message": "string",
  "data": {
    "id": "string",
    "productId": "string",
    "productClassificationId": "string",
    "stockLocationId": "string",
    "notes": "string | undefined",
    "buyingPrice": "number",
    "expectedSellingPrice": "number"
  }
}
```

---

## 2. GET /stocks

**Request**

- Query parameters (all optional):
  - `productId`: string
  - `productClassificationId`: string
  - `stockLocationId`: string
  - Pagination params (e.g., `page`, `limit`)

**Response**
```json
{
  "code": "string",
  "message": "string",
  "data": [
    {
      "id": "string",
      "productId": "string",
      "productClassificationId": "string",
      "stockLocationId": "string",
      "notes": "string | undefined",
      "buyingPrice": "number",
      "expectedSellingPrice": "number"
    }
    // ...more stocks
  ]
}
```

---

## 3. POST /stocks

**Request**
```json
{
  "productId": "string",
  "productClassificationId": "string",
  "stockLocationId": "string",
  "notes": "string | undefined",
  "buyingPrice": "number",
  "expectedSellingPrice": "number"
}
```

**Response**
```json
{
  "code": "string",
  "message": "string",
  "data": {
    "id": "string",
    "productId": "string",
    "productClassificationId": "string",
    "stockLocationId": "string",
    "notes": "string | undefined",
    "buyingPrice": "number",
    "expectedSellingPrice": "number"
  }
}
```

---

## 4. PUT /stocks/:id

**Request**

- Path parameter:
  - `id`: string

- Body (all fields optional, but at least one must be present):
```json
{
  "productId": "string",
  "productClassificationId": "string",
  "stockLocationId": "string",
  "notes": "string | undefined",
  "buyingPrice": "number",
  "expectedSellingPrice": "number"
}
```

**Response**
```json
{
  "code": "string",
  "message": "string",
  "data": {
    "id": "string",
    "productId": "string",
    "productClassificationId": "string",
    "stockLocationId": "string",
    "notes": "string | undefined",
    "buyingPrice": "number",
    "expectedSellingPrice": "number"
  }
}
```

---

## 5. DELETE /stocks/:id

**Request**

- Path parameter:
  - `id`: string

**Response**
```json
{
  "code": "string",
  "message": "string"
}
``` 