{"endpoints": [{"method": "POST", "path": "/stock/create", "request": {"interactionId": "string|undefined", "tenantId": "string", "productId": "string", "productCategoryId": "string", "quantity": "number"}, "response": {"stockId": "string", "tenantId": "string", "productId": "string", "productCategoryId": "string", "quantity": "number"}}, {"method": "POST", "path": "/stock/get", "request": {"interactionId": "string|undefined", "tenantId": "string", "stockId": "string"}, "response": {"stockId": "string", "tenantId": "string", "productId": "string", "productCategoryId": "string", "quantity": "number"}}, {"method": "POST", "path": "/stock/update", "request": {"interactionId": "string|undefined", "tenantId": "string", "stockId": "string", "productId": "string|undefined", "productCategoryId": "string|undefined", "quantity": "number|undefined"}, "response": {"stockId": "string", "tenantId": "string", "updated": true}}, {"method": "POST", "path": "/stock/delete", "request": {"interactionId": "string|undefined", "tenantId": "string", "stockId": "string"}, "response": {"stockId": "string", "tenantId": "string", "deleted": true}}, {"method": "POST", "path": "/stock/list", "request": {"interactionId": "string|undefined", "tenantId": "string", "page": "number|undefined", "pageSize": "number|undefined"}, "response": {"stocks": [{"stockId": "string", "tenantId": "string", "productId": "string", "productCategoryId": "string", "quantity": "number"}], "total": "number"}}]}