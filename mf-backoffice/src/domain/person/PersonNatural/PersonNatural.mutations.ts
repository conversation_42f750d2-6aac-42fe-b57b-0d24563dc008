import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { PersonNatural } from '@app/domain/person/PersonNatural/PersonNatural'
import {
  postPersonNatural,
  putPersonNatural,
  getPersonNaturals,
  getPersonNaturalById,
  getPersonNaturalByPersonId,
  deletePersonNatural,
} from '@app/domain/person/PersonNatural/PersonNatural.api'

export const useGetPersonNaturalsQuery = () => {
  const getPersonNaturalFn = useQuery({
    queryKey: ['getPersonNaturals'],
    queryFn: () => getPersonNaturals(),
  })

  return getPersonNaturalFn
}

export const useGetPersonNaturalByIdQuery = (id: string) => {
  const getPersonNaturalByIdFn = useQuery({
    queryKey: ['getPersonNaturalById', id],
    queryFn: () => getPersonNaturalById(id),
    enabled: id !== null,
  })

  return getPersonNaturalByIdFn
}

export const useGetPersonNaturalByPersonIdQuery = (personId: string) => {
  const getPersonNaturalByPersonIdFn = useQuery({
    queryKey: ['getPersonNaturalByPersonId', personId],
    queryFn: () => getPersonNaturalByPersonId(personId),
    enabled: personId !== null,
  })

  return getPersonNaturalByPersonIdFn
}

export const usePostPersonNaturalMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PersonNatural) => postPersonNatural(params),
  })

  const postPersonNaturalAsync = useCallback(
    async (props: PersonNatural) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    postPersonNaturalAsync,
    loading: isPending,
  }
}

export const usePutPersonNaturalMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: PersonNatural) => putPersonNatural(params),
  })

  const putPersonNaturalAsync = useCallback(
    async (props: PersonNatural) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    putPersonNaturalAsync,
    loading: isPending,
  }
}

export const useDeletePersonNaturalMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deletePersonNatural(id),
  })

  const deletePersonNaturalAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response.data
    },
    [mutateAsync],
  )

  return {
    deletePersonNaturalAsync,
    loading: isPending,
  }
}
