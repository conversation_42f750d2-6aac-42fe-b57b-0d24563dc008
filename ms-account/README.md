# Thrift Account Application
Account application service

## How to use this Application
Read the this [documentation](https://github.com/thrift-technology/docs/blob/main/development/backend.md).

## Authentication Architecture

The authentication system follows the Single Responsibility Principle with clear separation of concerns:

### Authentication Flow
1. **Authentication**: `authenticateByEmailAndPassword()` - Validates credentials and returns account ID
2. **Token Generation**: Resource layer generates JWT tokens using the account ID
3. **User Profile**: `getUserProfile()` - Fetches complete user information separately

### Endpoints
- `POST /authenticate` - Authenticate user and return tokens + profile
- `GET /profile` - Get user profile information (requires authentication)

### Response Structure
```json
{
  "code": "C_201_0201",
  "message": "Registro criado com sucesso",
  "data": {
    "accountId": "uuid",
    "personId": "uuid",
    "email": "<EMAIL>",
    "fullname": "User Name",
    "roleName": "Admin"
  }
}
```