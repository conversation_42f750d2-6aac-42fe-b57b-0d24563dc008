model Account {
  id                String    @id @default(uuid())
  email             String    @unique
  password          String
  passwordExpiresAt DateTime?
  isActive          Boolean   @default(true)
  personId          String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  deleted           <PERSON><PERSON>an   @default(false)

  roleId String?
  role   Role?   @relation(fields: [roleId], references: [id])

  @@map("accounts")
}
