import { validateWithZod } from '@thrift/common/engines/Validation'

import type { CreateAccountDto } from '@app/application/Account'
import { CreateAccountSchema } from '@app/application/Account/Account.schema'
import { Language } from '@app/application/Language'
import { Person } from '@app/application/Person'

import { Account } from '@app/domain/database/Account/Account.class'
import { Role } from '@app/domain/database/Role'

export class Create {
  public async create(input: unknown) {
    const dto: CreateAccountDto = validateWithZod(CreateAccountSchema, input)

    const { email, password, isActive, roleId, name, birthDate, gender } = dto

    const model = new Account()

    // Check if the email already exists
    const existingAccount = await model.findByEmail(email)

    if (existingAccount) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'account.email',
        }),
      )
    }

    // Check if the roleId exists
    const roleModel = new Role()
    const existingRole = await roleModel.findRole(roleId)

    if (!existingRole) {
      throw new ReferenceError(
        Language.translate('common:invalid', {
          name: 'account.roleId',
        }),
      )
    }

    const personModel = new Person()

    const personData = await personModel.create({
      name,
      birthDate,
      gender,
    })

    return model.create({
      email,
      password,
      isActive,
      roleId,
      personId: personData.id,
    })
  }
}
