import type { UpdateAccountDto } from '@app/application/Account/Account'
import { Language } from '@app/application/Language'

import { Account } from '@app/domain/database/Account/Account.class'
import { Role } from '@app/domain/database/Role'

export class Update {
  public async update({
    id,
    email,
    password,
    isActive,
    roleId,
  }: UpdateAccountDto) {
    const model = new Account()

    // Check if the account exists
    const existingAccount = await model.findById(id)

    if (!existingAccount) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'account' }),
      )
    }

    // Check if the email already exists (if email is being updated)
    if (email) {
      const existingEmail = await model.findByEmail(email)

      if (existingEmail && existingEmail.id !== id) {
        throw new ReferenceError(
          Language.translate('common:invalid', { name: 'account.email' }),
        )
      }
    }

    // Check if the roleId exists
    const roleModel = new Role()
    const existingRole = await roleModel.findRole(roleId)

    if (!existingRole) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'account.roleId' }),
      )
    }

    // Check if the password is empty
    if (!password || password.trim() === '') {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'account.password' }),
      )
    }

    const item = await model.update({
      id,
      email,
      password,
      isActive,
      roleId,
    })

    return item
  }
}
