import { z } from 'zod'

export const CreateAccountSchema = z.object({
  email: z.email().min(5).max(255),
  password: z.string().min(3),
  isActive: z.boolean().optional(),
  roleId: z.string().min(3),
  name: z.string().min(3),
  birthDate: z.coerce.date(),
  gender: z.enum(['male', 'female', 'other']),
})

export const UpdateAccountSchema = CreateAccountSchema.extend({
  id: z.uuid(),
  email: z.email().min(5).max(255).optional(),
  password: z.string().min(3).optional(),
  isActive: z.boolean().optional(),
  roleId: z.string().min(3).optional(),
  name: z.string().min(3).optional(),
  birthDate: z.coerce.date().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
})
