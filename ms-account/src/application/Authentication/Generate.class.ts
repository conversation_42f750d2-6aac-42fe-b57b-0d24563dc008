import { decodeRefreshToken, generateToken } from '@credentials/authorization'

import { validateWithZod } from '@thrift/common/engines/Validation'

import type {
  AuthenticationResponse,
  GenerateTokenProps,
  RefreshTokenProps,
  TokenResult,
} from '@app/application/Authentication'
import {
  AuthenticateSchema,
  RefreshTokenSchema,
} from '@app/application/Authentication/Authentication.schema'

import { Account } from '@app/domain/database/Account'
import { Person } from '@app/domain/database/Person'

export class Generate {
  public async generateTokenByEmailAndPassword(
    input: unknown,
  ): Promise<AuthenticationResponse> {
    const { email, password }: GenerateTokenProps = validateWithZod(
      AuthenticateSchema,
      input,
    )

    const accountModel = new Account()
    const personModel = new Person()

    const account = await accountModel.findByCredentials({
      email,
      password,
    })

    // Get person information if personId exists
    let fullname = ''

    if (account.personId) {
      try {
        const personResponse = await personModel.findById(account.personId)
        const personData = personResponse.data

        // Handle both PersonNaturalModel and PersonLegalModel
        fullname =
          'name' in personData
            ? personData.name || ''
            : personData.legalName || ''
      } catch {
        // If person not found, use empty string
        fullname = ''
      }
    }

    return {
      accountId: account.id,
      personId: account.personId || '',
      email: account.email,
      fullname,
      roleName: account.role?.name || '',
    }
  }

  public async generateTokenByRefrashToken(
    input: unknown,
  ): Promise<TokenResult> {
    const { refreshToken }: RefreshTokenProps = validateWithZod(
      RefreshTokenSchema,
      input,
    )

    const { ref } = decodeRefreshToken(refreshToken)
    const userId = ref as string

    return { token: generateToken(userId) }
  }
}
