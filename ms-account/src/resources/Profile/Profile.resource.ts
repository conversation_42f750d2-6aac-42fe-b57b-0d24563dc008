import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'
import { Get } from '@thrift/common/engines/Server'

import {
  Generate,
  type UserProfileResponse,
} from '@app/application/Authentication'
import { Language } from '@app/application/Language'

export class Profile {
  @Get('/profile')
  public async getProfile({ request, response }: ResourceMethodProps) {
    try {
      // Get user ID from the authenticated request
      // This assumes the middleware has already validated the token and set the user ID
      const userId = request.user?.id || request.headers()['x-user-id']

      if (!userId) {
        throw new ReferenceError('User ID not found in request')
      }

      const generateService = new Generate()
      const userProfile = await generateService.getUserProfile(userId)

      response.send<UserProfileResponse>({
        code: ResourceMessageCode.C_200_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_200_0001}`,
          { title: 'profile' },
        ),
        data: userProfile,
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof NotFoundException) {
        response.send<string>({
          code: ResourceMessageCode.C_404_0404,
          message: Language.translate('common:notFound', { name: 'profile' }),
        })
      } else if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0001,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_400_0001}`,
            { parameter: 'user authentication' },
          ),
        })
      } else {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
