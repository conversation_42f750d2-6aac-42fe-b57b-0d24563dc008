import {
  decodeRefreshToken,
  generateRefreshToken,
  generateToken,
} from '@credentials/authorization'

import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { Post } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'

// Extend ResourceResponse interface to include setHeader method
interface ExtendedResourceResponse {
  send: <T>(props: {
    code: ResourceMessageCode
    message: string
    data?: T | null
  }) => void
  setHeader: (name: string, value: string) => void
}

export class RefreshToken {
  @Post('/authenticate/refresh')
  public async generateNewToken({ request, response }: ResourceMethodProps) {
    try {
      // Get refresh token from headers
      const headers = request.headers() as { 'x-refresh-token'?: string }
      const refreshToken = headers['x-refresh-token']

      if (!refreshToken) {
        throw new ReferenceError('Refresh token is required in headers')
      }

      // Decode the refresh token to get the user ID
      const { ref } = decodeRefreshToken(refreshToken)
      const userId = ref as string

      // Generate new tokens
      const newToken = generateToken(userId)
      const newRefreshToken = generateRefreshToken(userId)

      // Cast response to extended interface to access setHeader method
      const extendedResponse = response as unknown as ExtendedResourceResponse

      extendedResponse.setHeader('Authorization', `Bearer ${newToken}`)
      extendedResponse.setHeader('X-Refresh-Token', newRefreshToken)

      response.send({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0201}`,
        ),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      response.send<string>({
        code: ResourceMessageCode.C_401_0001,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_401_0001}`,
          { title: 'refresh token' },
        ),
      })
    }
  }
}
