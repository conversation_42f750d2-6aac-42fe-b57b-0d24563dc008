import { generateRefreshToken, generateToken } from '@credentials/authorization'

import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { Post } from '@thrift/common/engines/Server'

import {
  Generate,
  type UserProfileResponse,
} from '@app/application/Authentication'
import { Language } from '@app/application/Language'

export class Authenticate {
  @Post('/authenticate')
  public async signIn({ request, response }: ResourceMethodProps) {
    try {
      const generateService = new Generate()

      // Step 1: Authenticate user and get account ID
      const authResult = await generateService.authenticateByEmailAndPassword(
        request.body(),
      )

      // Step 2: Generate tokens
      const token = generateToken(authResult.accountId)
      const refreshToken = generateRefreshToken(authResult.accountId)

      // Step 3: Get user profile information
      const userProfile = await generateService.getUserProfile(
        authResult.accountId,
      )

      response.setHeader('Authorization', `Bearer ${token}`)
      response.setHeader('X-Refresh-Token', refreshToken)

      response.send<UserProfileResponse>({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0201}`,
        ),
        data: userProfile,
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0001,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_400_0001}`,
            { parameter: 'email or password' },
          ),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resources:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
