import { PrismaClient } from '@prisma/client'
import config from 'app.config.json'
import { hashSync } from 'bcryptjs'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  const adminRole = await prisma.role.findUnique({
    where: {
      shortname: config.roles.admin.shortname,
    },
  })

  await prisma.account.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashSync('test-123-123', 10),
      roleId: adminRole!.id,
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
