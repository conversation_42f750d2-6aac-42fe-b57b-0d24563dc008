import { PrismaClient } from '@prisma/client'
import { compare, hashSync } from 'bcryptjs'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateAccountDto,
  CredentialsProps,
  FetchAccountsProps,
  UpdateAccountDto,
} from '@app/domain/database/Account'
import i18next from '@app/domain/locales/i18n/i18next'

export class Account {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findByCredentials({ email, password }: CredentialsProps) {
    const account = await this.prisma.account.findFirst({
      where: { email, deleted: false },
      include: {
        role: true,
      },
    })

    if (!account) {
      throw new ReferenceError(i18next.t('common:notFound', { name: 'email' }))
    }

    if (!(await compare(password, account.password))) {
      throw new ReferenceError(
        i18next.t('database:invalid', { name: 'password' }),
      )
    }

    return account
  }

  public async findById(id: string) {
    const account = await this.prisma.account.findFirst({
      where: { id, deleted: false },
      select: {
        id: true,
        email: true,
        isActive: true,
        personId: true,
        role: {
          select: {
            id: true,
            name: true,
            shortname: true,
          },
        },
      },
    })

    if (!account) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'account' }),
      )
    }

    return account
  }

  public async findByEmail(email: string) {
    return this.prisma.account.findFirst({
      where: { email, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchAccountsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [{ email: { contains: search } }],
      }),
    }

    const [accounts, totalItems] = await this.prisma.$transaction([
      this.prisma.account.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: {
          id: true,
          email: true,
          isActive: true,
          role: {
            select: {
              id: true,
              name: true,
              shortname: true,
            },
          },
        },
      }),
      this.prisma.account.count({ where }),
    ])

    return {
      items: accounts,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    email,
    password,
    isActive,
    roleId,
    personId,
  }: CreateAccountDto) {
    const account = await this.prisma.account.create({
      data: {
        email,
        password: hashSync(password, 10),
        isActive,
        roleId,
        personId,
      },
      select: {
        id: true,
        email: true,
        isActive: true,
        personId: true,
        role: {
          select: {
            id: true,
            name: true,
            shortname: true,
          },
        },
      },
    })

    return account
  }

  public async update({
    id,
    email,
    password,
    isActive,
    roleId,
  }: UpdateAccountDto) {
    const account = await this.prisma.account.update({
      where: { id },
      data: {
        email,
        password: hashSync(password, 10),
        isActive,
        roleId,
      },
    })

    return account
  }

  public async delete(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { deleted: true },
    })

    return account
  }

  public async deactivate(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { isActive: false },
    })

    return account
  }

  public async activate(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { isActive: true },
    })

    return account
  }
}
