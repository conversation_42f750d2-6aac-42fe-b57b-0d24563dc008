import { z } from 'zod'

/**
 * Common pagination and filtering schema for use across microservices
 * This schema provides standardized filtering, sorting, and pagination parameters
 * that can be extended by specific resource schemas
 */
export const PaginationFilterSchema = z.object({
  // Filtering
  id: z.uuid().optional(),
  search: z.string().optional(),

  // Pagination
  page: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => (val ? Number(val) : undefined)),
  perPage: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => (val ? Number(val) : undefined)),

  // Sorting
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
})

/**
 * Type definition for the pagination filter schema
 */
export type PaginationFilterDto = z.infer<typeof PaginationFilterSchema>

/**
 * Helper function to create a resource-specific filter schema
 * by extending the base pagination filter schema
 *
 * @param schema - The resource-specific schema to extend the base schema with
 * @returns A new schema that includes both pagination and resource-specific filters
 */
export function createFilterSchema<T extends z.ZodRawShape>(schema: T) {
  return PaginationFilterSchema.extend(schema)
}
