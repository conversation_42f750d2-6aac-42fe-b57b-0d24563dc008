import { ZodError } from 'zod'

import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceResponse,
} from '@thrift/common/engines/Resource'
import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

export class ResponseHelper {
  public static handleError(
    error: unknown,
    response: ResourceResponse,
    entityName = 'entity',
    translate: (key: string, options?: Record<string, unknown>) => string,
  ): void {
    Debug.error({
      message: error instanceof Error ? error.message : String(error),
      error,
    })

    if (error instanceof ZodError) {
      const formattedErrors = error.issues.map((err) => ({
        path: err.path.join('.'),
        message: err.message,
      }))

      response.send({
        code: ResourceMessageCode.C_400_0400,
        message:
          translate('common:validation', { name: entityName }) ||
          'Validation failed',
        data: { errors: formattedErrors },
      })
    } else if (error instanceof ReferenceError) {
      response.send({
        code: ResourceMessageCode.C_400_0400,
        message: translate('common:invalid', { name: entityName }),
      })
    } else if (error instanceof NotFoundException) {
      response.send({
        code: ResourceMessageCode.C_404_0404,
        message: translate('common:notFound', { name: entityName }),
      })
    } else if (error instanceof ConflictException) {
      response.send({
        code: ResourceMessageCode.C_409_0409,
        message: error.message,
      })
    } else {
      response.send({
        code: ResourceMessageCode.C_500_0500,
        message: translate(`resources:${ResourceMessageCode.C_500_0500}`),
      })
    }
  }

  public static sendSuccessResponse<T>(
    response: ResourceResponse,
    data: T,
    code = ResourceMessageCode.C_200_0200,
    translate: (key: string, options?: Record<string, unknown>) => string,
    options?: Record<string, unknown>,
  ): void {
    response.send({
      code,
      message: translate(`resources:${code}`, options),
      data,
    })
  }
}
