import type { ResourceMessageCode } from '@thrift/common/engines/Resource'

type ResourceRequestHeaders = {
  authorization?: string
  'x-refresh-token'?: string
}

export type ResourceRequest = {
  body: <B = unknown>() => B
  headers: () => ResourceRequestHeaders
  params: <P = unknown>() => P
  query: <Q = unknown>() => Q
  url: <Q = unknown>() => Q
}

export type ResourceResponseSendProps<T = unknown> = {
  code: ResourceMessageCode
  message: string
  data?: T | null
}

export type ResourceResponse = {
  send: <T>(props: ResourceResponseSendProps<T>) => void
  setHeader: (name: string, value: string) => void
}

export type ResourceMethodProps = {
  request: ResourceRequest
  response: ResourceResponse
}

export type ResourceMiddlewareMethodProps = ResourceMethodProps & {
  next: (error?: Error) => void
}
