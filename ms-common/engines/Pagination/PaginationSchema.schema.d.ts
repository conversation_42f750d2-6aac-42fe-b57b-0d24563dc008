import { z } from 'zod';
/**
 * Common pagination and filtering schema for use across microservices
 * This schema provides standardized filtering, sorting, and pagination parameters
 * that can be extended by specific resource schemas
 */
export declare const PaginationFilterSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodUUID>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    perPage: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodOptional<z.ZodEnum<{
        asc: "asc";
        desc: "desc";
    }>>;
}, z.core.$strip>;
/**
 * Type definition for the pagination filter schema
 */
export type PaginationFilterDto = z.infer<typeof PaginationFilterSchema>;
/**
 * Helper function to create a resource-specific filter schema
 * by extending the base pagination filter schema
 *
 * @param schema - The resource-specific schema to extend the base schema with
 * @returns A new schema that includes both pagination and resource-specific filters
 */
export declare function createFilterSchema<T extends z.ZodRawShape>(schema: T): z.ZodObject<(("id" | "search" | "page" | "perPage" | "sortBy" | "sortOrder") & keyof T extends never ? {
    id: z.ZodOptional<z.ZodUUID>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    perPage: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodOptional<z.ZodEnum<{
        asc: "asc";
        desc: "desc";
    }>>;
} & T : ({
    id: z.ZodOptional<z.ZodUUID>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    perPage: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodOptional<z.ZodEnum<{
        asc: "asc";
        desc: "desc";
    }>>;
} extends infer T_3 extends z.core.util.SomeObject ? { [K in keyof T_3 as K extends keyof T ? never : K]: {
    id: z.ZodOptional<z.ZodUUID>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    perPage: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodOptional<z.ZodEnum<{
        asc: "asc";
        desc: "desc";
    }>>;
}[K]; } : never) & { [K_1 in keyof T]: T[K_1]; }) extends infer T_1 ? { [k in keyof T_1]: (("id" | "search" | "page" | "perPage" | "sortBy" | "sortOrder") & keyof T extends never ? {
    id: z.ZodOptional<z.ZodUUID>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    perPage: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodOptional<z.ZodEnum<{
        asc: "asc";
        desc: "desc";
    }>>;
} & T : ({
    id: z.ZodOptional<z.ZodUUID>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    perPage: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodOptional<z.ZodEnum<{
        asc: "asc";
        desc: "desc";
    }>>;
} extends infer T_2 extends z.core.util.SomeObject ? { [K in keyof T_2 as K extends keyof T ? never : K]: {
    id: z.ZodOptional<z.ZodUUID>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    perPage: z.ZodPipe<z.ZodOptional<z.ZodUnion<readonly [z.ZodString, z.ZodNumber]>>, z.ZodTransform<number | undefined, string | number | undefined>>;
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodOptional<z.ZodEnum<{
        asc: "asc";
        desc: "desc";
    }>>;
}[K]; } : never) & { [K_1 in keyof T]: T[K_1]; })[k]; } : never, z.core.$strip>;
