"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseHelper = void 0;
const zod_1 = require("zod");
const Debug_1 = require("../../Debug");
const Resource_1 = require("../../Resource");
const ConflictException_1 = __importDefault(require("../../Resource/exceptions/ConflictException"));
const NotFoundException_1 = __importDefault(require("../../Resource/exceptions/NotFoundException"));
class ResponseHelper {
    static handleError(error, response, entityName = 'entity', translate) {
        Debug_1.Debug.error({
            message: error instanceof Error ? error.message : String(error),
            error,
        });
        if (error instanceof zod_1.ZodError) {
            const formattedErrors = error.issues.map((err) => ({
                path: err.path.join('.'),
                message: err.message,
            }));
            response.send({
                code: Resource_1.ResourceMessageCode.C_400_0400,
                message: translate('common:validation', { name: entityName }) ||
                    'Validation failed',
                data: { errors: formattedErrors },
            });
        }
        else if (error instanceof ReferenceError) {
            response.send({
                code: Resource_1.ResourceMessageCode.C_400_0400,
                message: translate('common:invalid', { name: entityName }),
            });
        }
        else if (error instanceof NotFoundException_1.default) {
            response.send({
                code: Resource_1.ResourceMessageCode.C_404_0404,
                message: translate('common:notFound', { name: entityName }),
            });
        }
        else if (error instanceof ConflictException_1.default) {
            response.send({
                code: Resource_1.ResourceMessageCode.C_409_0409,
                message: error.message,
            });
        }
        else {
            response.send({
                code: Resource_1.ResourceMessageCode.C_500_0500,
                message: translate(`resources:${Resource_1.ResourceMessageCode.C_500_0500}`),
            });
        }
    }
    static sendSuccessResponse(response, data, code = Resource_1.ResourceMessageCode.C_200_0200, translate, options) {
        response.send({
            code,
            message: translate(`resources:${code}`, options),
            data,
        });
    }
}
exports.ResponseHelper = ResponseHelper;
//# sourceMappingURL=ResponseHelper.js.map