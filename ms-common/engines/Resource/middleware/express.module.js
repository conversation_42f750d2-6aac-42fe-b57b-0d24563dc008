"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerCredentials = exports.triggerResources = exports.prepareResponse = exports.prepareRequest = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const Debug_1 = require("../../Debug");
const Resource_1 = require("../../Resource");
const prepareRequest = (request) => ({
    body: () => request.body,
    headers: () => request.headers,
    params: () => request.params,
    query: () => request.query,
    url: () => request.url,
});
exports.prepareRequest = prepareRequest;
const prepareResponse = (response) => ({
    send: ({ code, message, data }) => {
        return response.status(Number(code.split('_')[1])).json({
            code,
            message,
            data: data || null,
        });
    },
    setHeader: (name, value) => {
        response.setHeader(name, value);
    },
});
exports.prepareResponse = prepareResponse;
const triggerResources = (resourcePathName) => {
    (0, fs_1.readdir)(resourcePathName, { withFileTypes: true }, (error, files) => {
        if (error) {
            Debug_1.Debug.error({ message: error.message });
        }
        else {
            files.forEach((file) => {
                if (file.isDirectory()) {
                    (0, exports.triggerResources)((0, path_1.resolve)(resourcePathName, file.name));
                }
                else if (file.isFile() &&
                    file.name.match(/^(.*)(\.resource\.)(js|ts)$/)) {
                    Promise.resolve(`${(0, path_1.resolve)(resourcePathName, file.name)}`).then(s => __importStar(require(s))).catch((error) => {
                        Debug_1.Debug.error({ message: error.message });
                    });
                }
            });
        }
    });
};
exports.triggerResources = triggerResources;
const registerCredentials = (credentialsPathName) => (request, response, next) => Promise.resolve(`${(0, path_1.resolve)(credentialsPathName, 'authorization')}`).then(s => __importStar(require(s))).then((module) => module.authorization({
    request: (0, exports.prepareRequest)(request),
    response: (0, exports.prepareResponse)(response),
    next,
}))
    .catch((error) => {
    Debug_1.Debug.error({ message: error.message });
    response.send({
        code: Resource_1.ResourceMessageCode.C_401_0401,
        message: 'Unauthorized not found credentials',
    });
    next(error);
});
exports.registerCredentials = registerCredentials;
//# sourceMappingURL=express.module.js.map