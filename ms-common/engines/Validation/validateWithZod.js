"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateWithZod = validateWithZod;
const zod_1 = require("zod");
/**
 * Validates input using a provided Zod schema.
 * Throws ZodError if validation fails.
 */
function validateWithZod(schema, input) {
    const result = schema.safeParse(input);
    if (!result.success) {
        throw new zod_1.ZodError(result.error.issues);
    }
    return result.data;
}
//# sourceMappingURL=validateWithZod.js.map