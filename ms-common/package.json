{"name": "@thrift/common", "version": "0.1.0", "private": true, "description": "Common base tools shared between the applications", "repository": {"type": "git", "url": "https://github.com/thrift-technology/ms-common.git"}, "author": "Thrift Technology <<EMAIL>>", "type": "commonjs", "main": "./engines/index.js", "types": "./engines/index.d.ts", "scripts": {"build": "yarn healthcheck && tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "format": "prettier --check .", "format:fix": "prettier --write .", "healthcheck": "yarn lint && yarn format && yarn typecheck", "lint": "eslint ./src --ext .ts", "lint:fix": "yarn lint --fix", "prepare": "husky", "typecheck": "tsc --noEmit"}, "dependencies": {"@eslint/js": "9.31.0", "@trivago/prettier-plugin-sort-imports": "5.2.2", "@tsconfig/recommended": "1.0.10", "axios": "1.11.0", "dotenv": "17.2.0", "eslint": "9.31.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-prettier": "5.5.3", "eslint-plugin-unused-imports": "4.1.4", "express": "5.1.0", "prettier": "3.6.2", "typescript-eslint": "8.38.0", "zod": "4.0.5"}, "devDependencies": {"@types/express": "5.0.3", "@types/node": "24.1.0", "husky": "^9.1.7", "tsc-alias": "1.8.16", "typescript": "5.8.3"}, "engines": {"node": ">=20 <25", "npm": ">=10 <12", "yarn": ">=1.22 <2"}}